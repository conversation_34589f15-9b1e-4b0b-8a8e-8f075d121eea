import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/location_service.dart';

class DriverStatusCard extends StatelessWidget {
  const DriverStatusCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthService, LocationService>(
      builder: (context, authService, locationService, child) {
        final driver = authService.currentDriver;
        print('Osama:check');
        print('Osama:check: ${driver}');

        return Card(
          elevation: 4,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      color: Theme.of(context).colorScheme.primary,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'حالة السائق',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Online/Offline Status
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: locationService.isOnline
                            ? Colors.green
                            : Colors.grey,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      locationService.isOnline ? 'متصل' : 'غير متصل',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: locationService.isOnline
                                ? Colors.green
                                : Colors.grey,
                          ),
                    ),
                    const Spacer(),
                    Switch(
                      value: locationService.isOnline,
                      onChanged: (value) async {
                        if (value) {
                          await locationService.goOnline();
                        } else {
                          await locationService.goOffline();
                        }
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Available/Busy Status
                Row(
                  children: [
                    Icon(
                      locationService.isFree ? Icons.check_circle : Icons.work,
                      color:
                          locationService.isFree ? Colors.green : Colors.orange,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      locationService.isFree ? 'متاح للمهام' : 'مشغول',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: locationService.isFree
                                ? Colors.green
                                : Colors.orange,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                    const Spacer(),
                    if (locationService.isOnline)
                      TextButton(
                        onPressed: () async {
                          if (locationService.isFree) {
                            await locationService.setBusy();
                          } else {
                            await locationService.setAvailable();
                          }
                        },
                        child: Text(
                          locationService.isFree
                              ? 'تعيين كمشغول'
                              : 'تعيين كمتاح',
                        ),
                      ),
                  ],
                ),

                if (driver != null) ...[
                  const Divider(height: 32),

                  // Driver Info
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem(
                          context,
                          'الاسم',
                          driver.name,
                          Icons.person_outline,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildInfoItem(
                          context,
                          'الحالة',
                          driver.status == 'active' ? 'نشط' : 'غير نشط',
                          Icons.info_outline,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem(
                          context,
                          'الفريق',
                          driver.team?.name ?? 'غير محدد',
                          Icons.group_outlined,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildInfoItem(
                          context,
                          'نوع المركبة',
                          driver.vehicleSize?.name ?? 'غير محدد',
                          Icons.local_shipping_outlined,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6),
                  ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
      ],
    );
  }
}
