import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/auth_service.dart';
import '../../services/task_service.dart';
import '../../services/location_service.dart';
import '../../services/wallet_service.dart';
import '../../services/notification_service.dart';
import '../../widgets/driver_status_card.dart';
import '../../widgets/quick_stats_card.dart';
import '../../widgets/recent_tasks_card.dart';
import '../../widgets/pending_task_card.dart';
import '../../widgets/earnings_summary_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final taskService = Provider.of<TaskService>(context, listen: false);
    final walletService = Provider.of<WalletService>(context, listen: false);
    final locationService =
        Provider.of<LocationService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);

    try {
      // Load initial data
      await Future.wait([
        taskService.getTasks(page: 1, perPage: 5),
        taskService.checkPendingTasks(),
        walletService.getWallet(),
        walletService.getEarningsStats(),
        walletService.getTransactions(page: 1, perPage: 10),
        locationService.getCurrentStatus(),
      ]);

      debugPrint('Home data loaded successfully');
    } catch (e) {
      debugPrint('Error loading home data: $e');
    }
  }

  Future<void> _refreshData() async {
    final authService = Provider.of<AuthService>(context, listen: false);

    try {
      debugPrint('Starting data refresh...');

      // Refresh driver profile data from server
      await authService.refreshDriverData();

      // Reload all data
      await _loadData();

      debugPrint('Data refresh completed successfully');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البيانات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error refreshing data: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في تحديث البيانات'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: CustomScrollView(
          slivers: [
            _buildAppBar(),
            SliverPadding(
              padding: const EdgeInsets.all(16),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  // Pending Task Card (if any)
                  const PendingTaskCard(),

                  // Driver Status Card
                  const DriverStatusCard(),

                  const SizedBox(height: 16),

                  // Quick Stats
                  const QuickStatsCard(),

                  const SizedBox(height: 16),

                  // Earnings Summary
                  const EarningsSummaryCard(),

                  const SizedBox(height: 16),

                  // Recent Tasks
                  const RecentTasksCard(),

                  const SizedBox(
                      height: 100), // Bottom padding for navigation bar
                ]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        final driver = authService.currentDriver;

        return SliverAppBar(
          expandedHeight: 120,
          floating: true,
          pinned: true,
          backgroundColor: Theme.of(context).colorScheme.primary,
          flexibleSpace: FlexibleSpaceBar(
            title: Text(
              'مرحباً ${driver?.name ?? 'السائق'}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            background: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.primary.withOpacity(0.8),
                  ],
                ),
              ),
            ),
          ),
          actions: [
            // Notifications
            Consumer<NotificationService>(
              builder: (context, notificationService, child) {
                return IconButton(
                  icon: Stack(
                    children: [
                      const Icon(Icons.notifications_outlined,
                          color: Colors.white),
                      if (notificationService.unreadCount > 0)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 16,
                              minHeight: 16,
                            ),
                            child: Text(
                              '${notificationService.unreadCount}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                    ],
                  ),
                  onPressed: () {
                    // TODO: Navigate to notifications screen
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('شاشة الإشعارات قريباً')),
                    );
                  },
                );
              },
            ),

            // Settings
            IconButton(
              icon: const Icon(Icons.settings_outlined, color: Colors.white),
              onPressed: () {
                Navigator.pushNamed(context, '/settings');
              },
            ),
          ],
        );
      },
    );
  }
}
