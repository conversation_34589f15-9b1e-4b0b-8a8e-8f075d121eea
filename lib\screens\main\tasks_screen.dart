import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/task_service.dart';
import '../../models/task.dart';
import '../../widgets/task_card.dart';

class TasksScreen extends StatefulWidget {
  const TasksScreen({super.key});

  @override
  State<TasksScreen> createState() => _TasksScreenState();
}

class _TasksScreenState extends State<TasksScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadTasks();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadTasks() async {
    final taskService = Provider.of<TaskService>(context, listen: false);
    await taskService.getTasks(page: 1, perPage: 20, refresh: true);
  }

  Future<void> _refreshTasks() async {
    await _loadTasks();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المهام'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshTasks,
            tooltip: 'تحديث المهام',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'المتاحة'),
            Tab(text: 'الحالية'),
            Tab(text: 'المكتملة'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAvailableTasks(),
          _buildCurrentTasks(),
          _buildCompletedTasks(),
        ],
      ),
    );
  }

  Widget _buildAvailableTasks() {
    return Consumer<TaskService>(
      builder: (context, taskService, child) {
        final availableTasks = taskService.availableTasks;

        if (taskService.isLoading && availableTasks.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('جاري تحميل المهام...'),
              ],
            ),
          );
        }

        if (taskService.hasError && availableTasks.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  taskService.errorMessage ?? 'حدث خطأ غير متوقع',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadTasks,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        if (availableTasks.isEmpty) {
          return RefreshIndicator(
            onRefresh: _refreshTasks,
            child: ListView(
              children: [
                SizedBox(height: MediaQuery.of(context).size.height * 0.3),
                const Center(
                  child: Column(
                    children: [
                      Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد مهام متاحة',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.w600),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'لا توجد مهام متاحة للقبول في الوقت الحالي',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _refreshTasks,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: availableTasks.length,
            itemBuilder: (context, index) {
              return TaskCard(
                task: availableTasks[index],
                onAccept: (task) => _acceptTask(task),
                onReject: (task) => _rejectTask(task),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildCurrentTasks() {
    return Consumer<TaskService>(
      builder: (context, taskService, child) {
        final currentTasks = taskService.activeTasks;

        if (taskService.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (currentTasks.isEmpty) {
          return _buildEmptyState('لا توجد مهام حالية');
        }

        return RefreshIndicator(
          onRefresh: _refreshTasks,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: currentTasks.length,
            itemBuilder: (context, index) {
              return TaskCard(
                task: currentTasks[index],
                onStatusUpdate: (task, status) =>
                    _updateTaskStatus(task, status),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildCompletedTasks() {
    return Consumer<TaskService>(
      builder: (context, taskService, child) {
        final completedTasks = taskService.taskHistory;

        if (taskService.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (completedTasks.isEmpty) {
          return _buildEmptyState('لا توجد مهام مكتملة');
        }

        return RefreshIndicator(
          onRefresh: () => taskService.getTaskHistory(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: completedTasks.length,
            itemBuilder: (context, index) {
              return TaskCard(
                task: completedTasks[index],
                isCompleted: true,
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
          ),
        ],
      ),
    );
  }

  Future<void> _acceptTask(Task task) async {
    final taskService = Provider.of<TaskService>(context, listen: false);

    final response = await taskService.acceptTask(task.id);

    if (response.isSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم قبول المهمة بنجاح')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(response.errorMessage)),
      );
    }
  }

  Future<void> _rejectTask(Task task) async {
    final taskService = Provider.of<TaskService>(context, listen: false);

    final response = await taskService.rejectTask(task.id);

    if (response.isSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم رفض المهمة')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(response.errorMessage)),
      );
    }
  }

  Future<void> _updateTaskStatus(Task task, String status) async {
    final taskService = Provider.of<TaskService>(context, listen: false);

    final response = await taskService.updateTaskStatus(task.id, status);

    if (response.isSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم تحديث حالة المهمة')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(response.errorMessage)),
      );
    }
  }
}
